# 健康管理系统流程图

## 1. 健康需求处理完整流程

```mermaid
graph TD
    A[发起健康需求] -->|语音/图文输入症状| A1(例："咳嗽、咽喉痛，持续1天")
    A1 --> A2(系统关联基础健康档案)

    B[本地模型初步响应] --> B1(调取家庭药箱缓存数据)
    B1 --> B2(反馈可用药品："咽炎片、止咳糖浆")
    B --> B3(采集实时体征：心率、体温、血氧)
    B3 --> B4(判断无紧急风险)

    C[云端模型深度分析] -->|内模型加密上传数据| C1("云e养大模型调用医学知识库")
    C1 --> C2(判断："急性咽炎可能，症状轻微")
    C2 --> C3(生成用药建议："咽炎片用法+温水含漱")
    C --> C4(调用本地资源库)
    C4 --> C5(推荐食疗店/居家食谱)

    D[资源匹配与服务衔接] --> D1(推送附近社区诊所信息)
    D1 --> D2(展示：距离、医生、预约时段)
    D --> D3(若选择送药)
    D3 --> D4(生成订单→推送给社区站长端)

    E[后续跟踪与健康管理] --> E1(24小时后提醒反馈症状)
    E1 --> E2(更新健康档案)
    E2 --> E3{症状是否加重?}
    E3 -->|是| E4(触发二次评估→推荐升级服务)
    E3 -->|否| E5(持续健康监测)

    F[延伸功能使用] --> F1(查看"咳嗽护理专题")
    F1 --> F2(学习饮食禁忌)
    F --> F3(发起康复指导需求)
    F3 --> F4(匹配康复师上门服务)

    A2 --> B;
    B4 --> C;
    C5 --> D;
    D4 --> E;
    E5 --> F;
```

## 2. 内外模型协同机制

```mermaid
graph TD
    subgraph 终端侧（内模型）
        A[接收用户输入] --> A1(语音/图文解析→结构化症状)
        A1 --> A2(本地隐私过滤：屏蔽未授权数据)
        A2 --> A3(调用本地缓存：家庭药箱/基础体征)
        A3 --> A4{本地能否闭环处理?}
        A4 -->|是| A5(内模型轻量化算法处理→直接反馈)
        A4 -->|否| A6(数据脱敏+加密传输)
    end

    subgraph 云端侧（外模型+云e养大模型）
        B[接收终端数据] --> B1(外模型：症状分类与初步归因)
        B1 --> B2{是否需深度推理?}
        B2 -->|是| B3("云e养大模型：自然语言理解+医学知识推理")
        B3 --> B4(生成健康评估/个性化建议)
        B2 -->|否| B5(外模型基础算法处理)
        B4 --> B6(调用外部资源库：医疗机构/服务数据)
        B6 --> B7(资源匹配算法→生成推荐列表)
        B7 --> B8(结果压缩+加密返回)
    end

    subgraph 协同与容错机制
        C[网络中断] --> C1(内模型切换至离线模式)
        C1 --> C2(优先保障基础功能：药箱查询/历史缓存)
        D[云端负载过高] --> D1(负载均衡调度备用节点)
        D1 --> D2(保障响应延迟≤3秒)
        E[模型迭代] --> E1(内模型OTA升级)
        E1 --> E2(外模型基于用户反馈优化)
        E2 --> E3("云e养大模型接入最新医疗知识库")
    end

    A6 --> B;
    B8 --> F[终端接收结果];
    F --> G(内模型补充本地细节→最终呈现给用户);
    G --> H(同步服务订单至中台→触发站长派单);
```

## 3. 系统架构层次图

```mermaid
graph TD
    subgraph 终端层（内模型）
        A[终端内模型] --> A1[健康监测]
        A --> A2[OCR药品识别]
        A --> A3[本地预警]
        A1 & A2 & A3 --> B[终端数据采集]
    end

    B --> C[数据加密传输]

    subgraph 云端层（外模型）
        C --> D[云端外模型]
        D --> D1["云e养大模型"]
        D --> D2[资源数据库]
        D1 & D2 --> E[深度分析与决策]
    end

    E --> F[结果返回终端]
    E --> G[推送至服务端（站长/医护）]

    subgraph 数据流闭环
        B --> H[本地预处理]
        H --> I{是否复杂任务?}
        I -->|是| C
        I -->|否| J[本地直接响应]
        E --> K[服务执行]
        K --> L[数据归档]
        L --> A[终端内模型]
    end
```

## 4. 具体使用场景示例

```mermaid
graph TD
    A[用户语音/图文输入"头痛"] --> B[终端内模型]

    B --> B1[① 调用本地药箱数据]
    B --> B2[② 采集实时体征（心率、血压等）]
    B1 & B2 --> C[数据加密上传至云端]

    C --> D["云e养大模型处理"]
    D --> D1[① 分析症状复杂度（如伴随高热则升级紧急度）]
    D --> D2[② 结合用户病史（如偏头痛史）生成评估结果]
    D --> D3[③ 调用医疗机构数据库匹配资源]

    D1 & D2 & D3 --> E[终端展示结果]
    E --> E1[① 用药建议（如家中有布洛芬）]
    E --> E2[② 紧急度分级（如"建议1小时内到社区诊所"）]
    E --> E3[③ 附近3家擅长头痛的诊所（含距离、可预约时段）]
    E --> E4[④ 食疗建议（如薄荷茶缓解胀痛）]

    E --> F[用户确认需求]
    F --> G[系统自动生成服务单]
    G --> H[推送至对应站长/机构执行]
```
