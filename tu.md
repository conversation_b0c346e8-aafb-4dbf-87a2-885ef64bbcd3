# 健康管理系统流程图

## 1. 健康需求处理完整流程

**流程介绍：**
这张图展示了用户从感到不适到获得健康服务的完整过程。整个流程分为 5 个主要阶段：

- **发起需求**：用户通过语音或图文描述症状（如"咳嗽、咽喉痛"）
- **本地响应**：系统立即调取家庭药箱数据，采集体征，给出初步建议
- **云端分析**：复杂情况下，云端大模型深度分析症状，生成专业用药建议
- **资源匹配**：推荐附近诊所、送药服务等，用户可直接预约或下单
- **持续跟踪**：24 小时后回访，更新健康档案，必要时升级服务

整个流程既保证了即时响应，又提供了专业的医疗建议和便民服务。

```mermaid
graph TD
    A[发起健康需求] -->|语音图文输入症状| A1[例:咳嗽咽喉痛持续1天]
    A1 --> A2[系统关联基础健康档案]

    B[本地模型初步响应] --> B1[调取家庭药箱缓存数据]
    B1 --> B2[反馈可用药品:咽炎片止咳糖浆]
    B --> B3[采集实时体征:心率体温血氧]
    B3 --> B4[判断无紧急风险]

    C[云端模型深度分析] -->|内模型加密上传数据| C1[云e养大模型调用医学知识库]
    C1 --> C2[判断:急性咽炎可能症状轻微]
    C2 --> C3[生成用药建议:咽炎片用法加温水含漱]
    C --> C4[调用本地资源库]
    C4 --> C5[推荐食疗店居家食谱]

    D[资源匹配与服务衔接] --> D1[推送附近社区诊所信息]
    D1 --> D2[展示:距离医生预约时段]
    D --> D3[若选择送药]
    D3 --> D4[生成订单推送给社区站长端]

    E[后续跟踪与健康管理] --> E1[24小时后提醒反馈症状]
    E1 --> E2[更新健康档案]
    E2 --> E3{症状是否加重}
    E3 -->|是| E4[触发二次评估推荐升级服务]
    E3 -->|否| E5[持续健康监测]

    F[延伸功能使用] --> F1[查看咳嗽护理专题]
    F1 --> F2[学习饮食禁忌]
    F --> F3[发起康复指导需求]
    F3 --> F4[匹配康复师上门服务]

    A2 --> B
    B4 --> C
    C5 --> D
    D4 --> E
    E5 --> F
```

## 2. 内外模型协同机制

**流程介绍：**
这张图展示了边缘计算与云端计算的协同架构，说明系统如何通过智能调度实现最优性能：

- **边缘推理引擎**：部署在终端的轻量化模型，支持本地实时推理和缓存机制
- **云端大模型集群**：基于 Transformer 架构的医疗专用大模型，具备深度学习和知识图谱能力
- **智能路由策略**：通过复杂度评估算法，动态决定任务分配（边缘处理 vs 云端调用）
- **离线容错机制**：本地模型具备独立推理能力，支持断网场景下的基础医疗咨询

该架构通过 API 网关实现负载均衡，确保低延迟响应和高可用性。

```mermaid
graph TD
    subgraph 边缘计算层
        A[NLP语义解析] --> A1[本地推理引擎]
        A1 --> A2{复杂度评估算法}
        A2 -->|低复杂度| A3[边缘模型推理]
        A2 -->|高复杂度| A4[API网关调用]
    end

    subgraph 云端计算层
        A4 --> B[负载均衡器]
        B --> B1[大模型推理集群]
        B1 --> B2[医学知识图谱查询]
        B2 --> B3[资源匹配算法]
    end

    subgraph 容错与同步
        C[网络断开] --> C1[离线模式切换]
        C1 --> C2[本地缓存数据调用]
        D[连接恢复] --> D1[增量数据同步]
    end

    B3 --> E[结果序列化返回]
    E --> F[终端渲染展示]
    A3 --> F
```

## 3. 系统架构层次图

**流程介绍：**
这张图展示了系统的技术架构，说明数据是如何在不同层级间流动的：

- **终端层**：用户的手机或设备，负责健康监测、药品识别、本地预警等基础功能
- **传输层**：数据加密传输，保护用户隐私安全
- **云端层**：强大的云端大模型和资源数据库，提供深度分析和决策支持
- **服务层**：将分析结果转化为实际服务，推送给医护人员或社区站长执行
- **数据闭环**：系统会根据任务复杂度智能选择处理方式，并将服务结果反馈回终端，形成完整的数据循环

这种分层架构确保了系统的稳定性、安全性和可扩展性。

```mermaid
graph TD
    subgraph 终端层内模型
        A[终端内模型] --> A1[健康监测]
        A --> A2[OCR药品识别]
        A --> A3[本地预警]
        A1 --> B[终端数据采集]
        A2 --> B
        A3 --> B
    end

    B --> C[数据加密传输]

    subgraph 云端层外模型
        C --> D[云端外模型]
        D --> D1[云e养大模型]
        D --> D2[资源数据库]
        D1 --> E[深度分析与决策]
        D2 --> E
    end

    E --> F[结果返回终端]
    E --> G[推送至服务端站长医护]

    subgraph 数据流闭环
        B --> H[本地预处理]
        H --> I{是否复杂任务}
        I -->|是| C
        I -->|否| J[本地直接响应]
        E --> K[服务执行]
        K --> L[数据归档]
        L --> A
    end
```

## 4. 具体使用场景示例

**流程介绍：**
这张图以"头痛"为例，展示了用户实际使用系统的完整过程：

- **症状输入**：用户说"我头痛"，系统立即启动分析流程
- **数据收集**：手机调用本地药箱数据，同时采集心率、血压等实时体征
- **智能分析**：云端大模型综合分析症状严重程度、用户病史、当前体征
- **个性化建议**：系统提供四个维度的建议：用药方案、紧急程度、就医选择、食疗建议
- **服务对接**：用户确认需求后，系统自动生成服务订单，推送给相应的医疗机构或社区服务人员

整个过程从症状输入到服务安排，通常在 1-2 分钟内完成，真正实现了"一句话解决健康问题"。

```mermaid
graph TD
    A[用户语音图文输入头痛] --> B[终端内模型]

    B --> B1[调用本地药箱数据]
    B --> B2[采集实时体征心率血压等]
    B1 --> C[数据加密上传至云端]
    B2 --> C

    C --> D[云e养大模型处理]
    D --> D1[分析症状复杂度如伴随高热则升级紧急度]
    D --> D2[结合用户病史如偏头痛史生成评估结果]
    D --> D3[调用医疗机构数据库匹配资源]

    D1 --> E[终端展示结果]
    D2 --> E
    D3 --> E
    E --> E1[用药建议如家中有布洛芬]
    E --> E2[紧急度分级如建议1小时内到社区诊所]
    E --> E3[附近3家擅长头痛的诊所含距离可预约时段]
    E --> E4[食疗建议如薄荷茶缓解胀痛]

    E --> F[用户确认需求]
    F --> G[系统自动生成服务单]
    G --> H[推送至对应站长机构执行]
```
