# 健康管理系统流程图

## 1. 健康需求处理完整流程

**流程介绍：**
这张图展示了用户从感到不适到获得健康服务的完整过程。整个流程分为 5 个主要阶段：

- **发起需求**：用户通过语音或图文描述症状（如"咳嗽、咽喉痛"）
- **本地响应**：系统立即调取家庭药箱数据，采集体征，给出初步建议
- **云端分析**：复杂情况下，云端大模型深度分析症状，生成专业用药建议
- **资源匹配**：推荐附近诊所、送药服务等，用户可直接预约或下单
- **持续跟踪**：24 小时后回访，更新健康档案，必要时升级服务

整个流程既保证了即时响应，又提供了专业的医疗建议和便民服务。

```mermaid
graph TD
    A[发起健康需求] -->|语音图文输入症状| A1[例:咳嗽咽喉痛持续1天]
    A1 --> A2[系统关联基础健康档案]

    B[本地模型初步响应] --> B1[调取家庭药箱缓存数据]
    B1 --> B2[反馈可用药品:咽炎片止咳糖浆]
    B --> B3[采集实时体征:心率体温血氧]
    B3 --> B4[判断无紧急风险]

    C[云端模型深度分析] -->|内模型加密上传数据| C1[云e养大模型调用医学知识库]
    C1 --> C2[判断:急性咽炎可能症状轻微]
    C2 --> C3[生成用药建议:咽炎片用法加温水含漱]
    C --> C4[调用本地资源库]
    C4 --> C5[推荐食疗店居家食谱]

    D[资源匹配与服务衔接] --> D1[推送附近社区诊所信息]
    D1 --> D2[展示:距离医生预约时段]
    D --> D3[若选择送药]
    D3 --> D4[生成订单推送给社区站长端]

    E[后续跟踪与健康管理] --> E1[24小时后提醒反馈症状]
    E1 --> E2[更新健康档案]
    E2 --> E3{症状是否加重}
    E3 -->|是| E4[触发二次评估推荐升级服务]
    E3 -->|否| E5[持续健康监测]

    F[延伸功能使用] --> F1[查看咳嗽护理专题]
    F1 --> F2[学习饮食禁忌]
    F --> F3[发起康复指导需求]
    F3 --> F4[匹配康复师上门服务]

    A2 --> B
    B4 --> C
    C5 --> D
    D4 --> E
    E5 --> F
```

## 2. 内外模型协同机制

**流程介绍：**
这张图说明了系统的"双重大脑"工作模式，让用户理解为什么有时响应很快，有时需要等待：

- **手机端智能助手**：就像随身医生，能快速处理常见小问题（如查药箱、测体温）
- **云端专家系统**：像专家会诊，处理复杂症状，调用庞大的医学知识库
- **智能分工**：系统自动判断问题难易，简单问题手机直接解决，复杂问题交给云端专家
- **离线保障**：即使没有网络，手机也能使用本地数据提供基础服务

这种设计既保证了响应速度，又确保了专业性，还能在各种网络环境下正常工作。

```mermaid
graph TD
    subgraph 手机端智能助手
        A[用户说出症状] --> A1[手机理解症状]
        A1 --> A2{问题简单吗}
        A2 -->|是| A3[直接给建议]
        A2 -->|否| A4[发送到云端]
    end

    subgraph 云端专家系统
        A4 --> B[云端大脑分析]
        B --> B1[查医学知识库]
        B1 --> B2[生成专业建议]
        B2 --> B3[匹配附近医疗资源]
    end

    subgraph 智能配合
        C[没网络时] --> C1[手机独立工作]
        C1 --> C2[使用本地药箱数据]
        D[网络恢复] --> D1[同步最新数据]
    end

    B3 --> E[返回完整方案]
    E --> F[手机展示给用户]
    A3 --> F
```

## 3. 系统架构层次图

**流程介绍：**
这张图展示了系统的技术架构，说明数据是如何在不同层级间流动的：

- **终端层**：用户的手机或设备，负责健康监测、药品识别、本地预警等基础功能
- **传输层**：数据加密传输，保护用户隐私安全
- **云端层**：强大的云端大模型和资源数据库，提供深度分析和决策支持
- **服务层**：将分析结果转化为实际服务，推送给医护人员或社区站长执行
- **数据闭环**：系统会根据任务复杂度智能选择处理方式，并将服务结果反馈回终端，形成完整的数据循环

这种分层架构确保了系统的稳定性、安全性和可扩展性。

```mermaid
graph TD
    subgraph 终端层内模型
        A[终端内模型] --> A1[健康监测]
        A --> A2[OCR药品识别]
        A --> A3[本地预警]
        A1 --> B[终端数据采集]
        A2 --> B
        A3 --> B
    end

    B --> C[数据加密传输]

    subgraph 云端层外模型
        C --> D[云端外模型]
        D --> D1[云e养大模型]
        D --> D2[资源数据库]
        D1 --> E[深度分析与决策]
        D2 --> E
    end

    E --> F[结果返回终端]
    E --> G[推送至服务端站长医护]

    subgraph 数据流闭环
        B --> H[本地预处理]
        H --> I{是否复杂任务}
        I -->|是| C
        I -->|否| J[本地直接响应]
        E --> K[服务执行]
        K --> L[数据归档]
        L --> A
    end
```

## 4. 具体使用场景示例

```mermaid
graph TD
    A[用户语音图文输入头痛] --> B[终端内模型]

    B --> B1[调用本地药箱数据]
    B --> B2[采集实时体征心率血压等]
    B1 --> C[数据加密上传至云端]
    B2 --> C

    C --> D[云e养大模型处理]
    D --> D1[分析症状复杂度如伴随高热则升级紧急度]
    D --> D2[结合用户病史如偏头痛史生成评估结果]
    D --> D3[调用医疗机构数据库匹配资源]

    D1 --> E[终端展示结果]
    D2 --> E
    D3 --> E
    E --> E1[用药建议如家中有布洛芬]
    E --> E2[紧急度分级如建议1小时内到社区诊所]
    E --> E3[附近3家擅长头痛的诊所含距离可预约时段]
    E --> E4[食疗建议如薄荷茶缓解胀痛]

    E --> F[用户确认需求]
    F --> G[系统自动生成服务单]
    G --> H[推送至对应站长机构执行]
```
