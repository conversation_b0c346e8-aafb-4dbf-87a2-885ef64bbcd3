# 健康管理系统流程图

## 1. 健康需求处理完整流程

```mermaid
graph TD
    A[发起健康需求] -->|语音图文输入症状| A1[例:咳嗽咽喉痛持续1天]
    A1 --> A2[系统关联基础健康档案]

    B[本地模型初步响应] --> B1[调取家庭药箱缓存数据]
    B1 --> B2[反馈可用药品:咽炎片止咳糖浆]
    B --> B3[采集实时体征:心率体温血氧]
    B3 --> B4[判断无紧急风险]

    C[云端模型深度分析] -->|内模型加密上传数据| C1[云e养大模型调用医学知识库]
    C1 --> C2[判断:急性咽炎可能症状轻微]
    C2 --> C3[生成用药建议:咽炎片用法加温水含漱]
    C --> C4[调用本地资源库]
    C4 --> C5[推荐食疗店居家食谱]

    D[资源匹配与服务衔接] --> D1[推送附近社区诊所信息]
    D1 --> D2[展示:距离医生预约时段]
    D --> D3[若选择送药]
    D3 --> D4[生成订单推送给社区站长端]

    E[后续跟踪与健康管理] --> E1[24小时后提醒反馈症状]
    E1 --> E2[更新健康档案]
    E2 --> E3{症状是否加重}
    E3 -->|是| E4[触发二次评估推荐升级服务]
    E3 -->|否| E5[持续健康监测]

    F[延伸功能使用] --> F1[查看咳嗽护理专题]
    F1 --> F2[学习饮食禁忌]
    F --> F3[发起康复指导需求]
    F3 --> F4[匹配康复师上门服务]

    A2 --> B
    B4 --> C
    C5 --> D
    D4 --> E
    E5 --> F
```

## 2. 内外模型协同机制

```mermaid
graph TD
    subgraph 终端侧内模型
        A[接收用户输入] --> A1[语音图文解析到结构化症状]
        A1 --> A2[本地隐私过滤屏蔽未授权数据]
        A2 --> A3[调用本地缓存家庭药箱基础体征]
        A3 --> A4{本地能否闭环处理}
        A4 -->|是| A5[内模型轻量化算法处理直接反馈]
        A4 -->|否| A6[数据脱敏加密传输]
    end

    subgraph 云端侧外模型云e养大模型
        B[接收终端数据] --> B1[外模型症状分类与初步归因]
        B1 --> B2{是否需深度推理}
        B2 -->|是| B3[云e养大模型自然语言理解医学知识推理]
        B3 --> B4[生成健康评估个性化建议]
        B2 -->|否| B5[外模型基础算法处理]
        B4 --> B6[调用外部资源库医疗机构服务数据]
        B6 --> B7[资源匹配算法生成推荐列表]
        B7 --> B8[结果压缩加密返回]
    end

    subgraph 协同与容错机制
        C[网络中断] --> C1[内模型切换至离线模式]
        C1 --> C2[优先保障基础功能药箱查询历史缓存]
        D[云端负载过高] --> D1[负载均衡调度备用节点]
        D1 --> D2[保障响应延迟小于等于3秒]
        E[模型迭代] --> E1[内模型OTA升级]
        E1 --> E2[外模型基于用户反馈优化]
        E2 --> E3[云e养大模型接入最新医疗知识库]
    end

    A6 --> B
    B8 --> F[终端接收结果]
    F --> G[内模型补充本地细节最终呈现给用户]
    G --> H[同步服务订单至中台触发站长派单]
```

## 3. 系统架构层次图

```mermaid
graph TD
    subgraph 终端层内模型
        A[终端内模型] --> A1[健康监测]
        A --> A2[OCR药品识别]
        A --> A3[本地预警]
        A1 --> B[终端数据采集]
        A2 --> B
        A3 --> B
    end

    B --> C[数据加密传输]

    subgraph 云端层外模型
        C --> D[云端外模型]
        D --> D1[云e养大模型]
        D --> D2[资源数据库]
        D1 --> E[深度分析与决策]
        D2 --> E
    end

    E --> F[结果返回终端]
    E --> G[推送至服务端站长医护]

    subgraph 数据流闭环
        B --> H[本地预处理]
        H --> I{是否复杂任务}
        I -->|是| C
        I -->|否| J[本地直接响应]
        E --> K[服务执行]
        K --> L[数据归档]
        L --> A
    end
```

## 4. 具体使用场景示例

```mermaid
graph TD
    A[用户语音图文输入头痛] --> B[终端内模型]

    B --> B1[调用本地药箱数据]
    B --> B2[采集实时体征心率血压等]
    B1 --> C[数据加密上传至云端]
    B2 --> C

    C --> D[云e养大模型处理]
    D --> D1[分析症状复杂度如伴随高热则升级紧急度]
    D --> D2[结合用户病史如偏头痛史生成评估结果]
    D --> D3[调用医疗机构数据库匹配资源]

    D1 --> E[终端展示结果]
    D2 --> E
    D3 --> E
    E --> E1[用药建议如家中有布洛芬]
    E --> E2[紧急度分级如建议1小时内到社区诊所]
    E --> E3[附近3家擅长头痛的诊所含距离可预约时段]
    E --> E4[食疗建议如薄荷茶缓解胀痛]

    E --> F[用户确认需求]
    F --> G[系统自动生成服务单]
    G --> H[推送至对应站长机构执行]
```
